# Offline Leetcode Platform

A simple, personal offline platform for practicing Leetcode problems with both brute force and optimized solutions.

## Features

- 📁 Organized problem structure by difficulty
- 🔄 Both brute force and optimized solutions for each problem
- ⚡ Built-in test runner with performance comparison
- 📊 Progress tracking and statistics
- 🎯 Focus on Blind 75 and popular interview problems
- 🐍 Pure Python implementation

## Directory Structure

```
leetcode-offline/
├── problems/
│   ├── easy/           # Easy difficulty problems
│   ├── medium/         # Medium difficulty problems
│   └── hard/           # Hard difficulty problems
├── utils/              # Utility modules
├── main.py            # Main CLI interface
├── problem_manager.py # Problem management
└── README.md
```

## Usage

### List all problems
```bash
python main.py list
```

### Run a specific problem
```bash
python main.py run two_sum
```

### Run all problems of a difficulty
```bash
python main.py run easy
python main.py run medium
```

### Add a new problem
```bash
python main.py add "Problem Name" easy
```

### Show statistics
```bash
python main.py stats
```

## Problem Structure

Each problem has its own directory containing:
- `problem.txt` - Problem statement and description
- `input.txt` - Sample input test cases
- `output.txt` - Expected output for test cases
- `brute_force.py` - Brute force solution
- `optimized.py` - Optimized solution

## Getting Started

1. Run `python main.py list` to see available problems
2. Try running `python main.py run two_sum` to test the system
3. Add your own problems with `python main.py add "New Problem" medium`
4. Practice and improve your solutions!

## Sample Problems Included

### Easy
- Two Sum
- Contains Duplicate  
- Best Time to Buy and Sell Stock

### Medium
- Maximum Subarray
- Product of Array Except Self

More problems can be easily added using the built-in problem manager.
