#!/usr/bin/env python3
"""
Main CLI interface for the offline Leetcode platform
"""
import sys
import argparse
from problem_manager import ProblemManager
from utils.test_runner import TestRunner


def print_banner():
    """Print welcome banner"""
    print("🎯 Offline Leetcode Platform")
    print("=" * 40)


def list_problems(manager: <PERSON><PERSON>anager):
    """List all available problems"""
    problems = manager.list_problems()
    
    if not any(problems.values()):
        print("No problems found. Add some problems first!")
        return
    
    for difficulty, problem_list in problems.items():
        if problem_list:
            print(f"\n📚 {difficulty.upper()} ({len(problem_list)} problems)")
            print("-" * 30)
            for i, problem in enumerate(sorted(problem_list), 1):
                print(f"{i:2d}. {problem.replace('_', ' ').title()}")


def run_problem(runner: <PERSON>Runner, problem_name: str, difficulty: str = None):
    """Run a specific problem"""
    results = runner.run_problem(problem_name, difficulty)
    runner.print_results(results)


def run_difficulty(runner: <PERSON><PERSON><PERSON><PERSON>, manager: <PERSON><PERSON><PERSON><PERSON>, difficulty: str):
    """Run all problems of a specific difficulty"""
    problems = manager.list_problems()
    
    if difficulty not in problems or not problems[difficulty]:
        print(f"No {difficulty} problems found.")
        return
    
    print(f"\n🚀 Running all {difficulty} problems...")
    print("=" * 50)
    
    total_problems = len(problems[difficulty])
    passed_problems = 0
    
    for problem_name in sorted(problems[difficulty]):
        results = runner.run_problem(problem_name, difficulty)
        
        # Quick summary for batch runs
        if "error" not in results:
            bf_passed = results.get("brute_force", {}).get("passed", 0)
            bf_total = results.get("brute_force", {}).get("total", 0)
            opt_passed = results.get("optimized", {}).get("passed", 0)
            opt_total = results.get("optimized", {}).get("total", 0)
            
            if (bf_passed == bf_total and bf_total > 0) or (opt_passed == opt_total and opt_total > 0):
                passed_problems += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"{status} {problem_name.replace('_', ' ').title()}")
        else:
            print(f"❌ {problem_name.replace('_', ' ').title()} - {results['error']}")
    
    print(f"\n📊 Summary: {passed_problems}/{total_problems} problems passed")


def show_stats(manager: ProblemManager):
    """Show platform statistics"""
    stats = manager.get_statistics()
    
    print("\n📊 Platform Statistics")
    print("=" * 30)
    print(f"Total Problems: {stats['total_problems']}")
    
    print("\nBy Difficulty:")
    for difficulty, count in stats['by_difficulty'].items():
        print(f"  {difficulty.title()}: {count}")
    
    print("\nCompletion Status:")
    completion = stats['completion']
    total = completion['completed'] + completion['partial'] + completion['empty']
    if total > 0:
        print(f"  ✅ Completed: {completion['completed']} ({completion['completed']/total*100:.1f}%)")
        print(f"  🔄 Partial: {completion['partial']} ({completion['partial']/total*100:.1f}%)")
        print(f"  📝 Empty: {completion['empty']} ({completion['empty']/total*100:.1f}%)")
    else:
        print("  No problems found")


def add_problem(manager: ProblemManager, problem_name: str, difficulty: str):
    """Add a new problem"""
    result = manager.add_problem(problem_name, difficulty)
    print(result)


def search_problems(manager: ProblemManager, query: str):
    """Search for problems"""
    results = manager.search_problems(query)
    
    found_any = any(results.values())
    if not found_any:
        print(f"No problems found matching '{query}'")
        return
    
    print(f"\n🔍 Search results for '{query}':")
    for difficulty, problems in results.items():
        if problems:
            print(f"\n{difficulty.upper()}:")
            for problem in sorted(problems):
                print(f"  • {problem.replace('_', ' ').title()}")


def main():
    parser = argparse.ArgumentParser(description="Offline Leetcode Platform")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    subparsers.add_parser('list', help='List all problems')
    
    # Run command
    run_parser = subparsers.add_parser('run', help='Run problem(s)')
    run_parser.add_argument('target', help='Problem name or difficulty (easy/medium/hard)')
    run_parser.add_argument('--difficulty', '-d', help='Specify difficulty when running by name')
    
    # Add command
    add_parser = subparsers.add_parser('add', help='Add a new problem')
    add_parser.add_argument('name', help='Problem name')
    add_parser.add_argument('difficulty', choices=['easy', 'medium', 'hard'], help='Problem difficulty')
    
    # Stats command
    subparsers.add_parser('stats', help='Show statistics')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search problems')
    search_parser.add_argument('query', help='Search query')
    
    args = parser.parse_args()
    
    # Initialize components
    manager = ProblemManager()
    runner = TestRunner()
    
    print_banner()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'list':
        list_problems(manager)
    
    elif args.command == 'run':
        target = args.target.lower()
        if target in ['easy', 'medium', 'hard']:
            run_difficulty(runner, manager, target)
        else:
            run_problem(runner, target, args.difficulty)
    
    elif args.command == 'add':
        add_problem(manager, args.name, args.difficulty)
    
    elif args.command == 'stats':
        show_stats(manager)
    
    elif args.command == 'search':
        search_problems(manager, args.query)


if __name__ == "__main__":
    main()
