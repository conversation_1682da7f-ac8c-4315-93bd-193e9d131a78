"""
Problem manager for the offline Leetcode platform
"""
import os
from utils.problem_template import ProblemTemplate


class ProblemManager:
    def __init__(self, base_dir: str = "problems"):
        self.base_dir = base_dir
        self.template = ProblemTemplate()
        
        # Ensure base directories exist
        for difficulty in ["easy", "medium", "hard"]:
            os.makedirs(os.path.join(base_dir, difficulty), exist_ok=True)
    
    def add_problem(self, problem_name: str, difficulty: str) -> str:
        """Add a new problem with template files"""
        if difficulty not in ["easy", "medium", "hard"]:
            return f"Error: Difficulty must be 'easy', 'medium', or 'hard'"
        
        # Convert problem name to snake_case
        problem_name = problem_name.lower().replace(' ', '_').replace('-', '_')
        
        # Check if problem already exists
        problem_path = os.path.join(self.base_dir, difficulty, problem_name)
        if os.path.exists(problem_path):
            return f"Error: Problem '{problem_name}' already exists in {difficulty}"
        
        # Create problem structure
        try:
            created_path = self.template.create_problem_structure(problem_name, difficulty, self.base_dir)
            return f"✅ Created problem '{problem_name}' in {difficulty}\nPath: {created_path}"
        except Exception as e:
            return f"Error creating problem: {str(e)}"
    
    def list_problems(self) -> dict:
        """List all available problems"""
        return self.template.list_problems(self.base_dir)
    
    def get_problem_info(self, problem_name: str, difficulty: str = None) -> dict:
        """Get detailed information about a problem"""
        return self.template.get_problem_info(problem_name, difficulty, self.base_dir)
    
    def get_statistics(self) -> dict:
        """Get statistics about problems and completion"""
        problems = self.list_problems()
        stats = {
            "total_problems": 0,
            "by_difficulty": {},
            "completion": {"completed": 0, "partial": 0, "empty": 0}
        }
        
        for difficulty, problem_list in problems.items():
            stats["by_difficulty"][difficulty] = len(problem_list)
            stats["total_problems"] += len(problem_list)
            
            # Check completion status for each problem
            for problem_name in problem_list:
                info = self.get_problem_info(problem_name, difficulty)
                if "files" in info:
                    files = info["files"]
                    has_brute = files.get("brute_force.py", False)
                    has_optimized = files.get("optimized.py", False)
                    
                    if has_brute and has_optimized:
                        # Check if files have actual implementations (not just templates)
                        brute_path = os.path.join(info["path"], "brute_force.py")
                        opt_path = os.path.join(info["path"], "optimized.py")
                        
                        brute_implemented = self._is_implemented(brute_path)
                        opt_implemented = self._is_implemented(opt_path)
                        
                        if brute_implemented and opt_implemented:
                            stats["completion"]["completed"] += 1
                        elif brute_implemented or opt_implemented:
                            stats["completion"]["partial"] += 1
                        else:
                            stats["completion"]["empty"] += 1
                    else:
                        stats["completion"]["empty"] += 1
        
        return stats
    
    def _is_implemented(self, file_path: str) -> bool:
        """Check if a solution file has been implemented (not just template)"""
        if not os.path.exists(file_path):
            return False
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                # Check if the solution function has been implemented
                # (not just "pass" or "TODO")
                if "# TODO: Implement your solution here" in content:
                    return False
                if content.count("pass") > 0 and "def solution" in content:
                    # Check if there's actual code after the function definition
                    lines = content.split('\n')
                    in_solution = False
                    for line in lines:
                        if "def solution" in line:
                            in_solution = True
                            continue
                        if in_solution and line.strip():
                            if line.strip() == "pass":
                                return False
                            if not line.strip().startswith('#') and not line.strip().startswith('"""'):
                                return True
                return True
        except:
            return False
    
    def remove_problem(self, problem_name: str, difficulty: str = None) -> str:
        """Remove a problem (with confirmation)"""
        info = self.get_problem_info(problem_name, difficulty)
        if "error" in info:
            return info["error"]
        
        import shutil
        try:
            shutil.rmtree(info["path"])
            return f"✅ Removed problem '{problem_name}' from {info['difficulty']}"
        except Exception as e:
            return f"Error removing problem: {str(e)}"
    
    def search_problems(self, query: str) -> dict:
        """Search for problems by name"""
        all_problems = self.list_problems()
        results = {"easy": [], "medium": [], "hard": []}
        
        query = query.lower()
        for difficulty, problems in all_problems.items():
            for problem in problems:
                if query in problem.lower():
                    results[difficulty].append(problem)
        
        return results
