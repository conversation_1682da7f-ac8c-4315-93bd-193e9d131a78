"""
Problem: Best Time to Buy and Sell Stock
Approach: Brute Force
Time Complexity: O(n²)
Space Complexity: O(1)

Explanation:
Check all possible buy-sell combinations.
For each buy day, check all possible sell days after it.
Keep track of maximum profit found.

Key insights:
- Must buy before selling (j > i)
- Check all valid buy-sell pairs
- Calculate profit for each pair
- Return maximum profit found
"""


def solution(prices):
    """
    Find maximum profit using brute force approach
    
    Args:
        prices: List of stock prices by day
    
    Returns:
        Maximum profit achievable
    """
    max_profit = 0
    n = len(prices)
    
    # Try all possible buy-sell combinations
    for i in range(n):
        for j in range(i + 1, n):
            profit = prices[j] - prices[i]
            max_profit = max(max_profit, profit)
    
    return max_profit


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    prices1 = [7, 1, 5, 3, 6, 4]
    result1 = solution(prices1)
    print(f"Input: prices = {prices1}")
    print(f"Output: {result1}")
    print("Explanation: Buy at price 1, sell at price 6, profit = 5")
    print()
    
    # Test case 2
    prices2 = [7, 6, 4, 3, 1]
    result2 = solution(prices2)
    print(f"Input: prices = {prices2}")
    print(f"Output: {result2}")
    print("Explanation: Prices only decrease, no profit possible")
    print()
    
    # Test case 3
    prices3 = [1, 2, 3, 4, 5]
    result3 = solution(prices3)
    print(f"Input: prices = {prices3}")
    print(f"Output: {result3}")
    print("Explanation: Buy at price 1, sell at price 5, profit = 4")
    print()


if __name__ == "__main__":
    main()
