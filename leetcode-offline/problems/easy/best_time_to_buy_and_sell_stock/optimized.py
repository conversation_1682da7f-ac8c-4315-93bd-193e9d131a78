"""
Problem: Best Time to Buy and Sell Stock
Approach: Single Pass (Optimized)
Time Complexity: O(n)
Space Complexity: O(1)

Explanation:
Keep track of the minimum price seen so far and maximum profit.
For each price, calculate profit if we sell today and bought at minimum price.
Update maximum profit and minimum price as we go.

Key insights:
- Track minimum buy price seen so far
- Calculate profit for selling at current price
- Update maximum profit if current profit is better
- Single pass through the array
"""


def solution(prices):
    """
    Find maximum profit using single pass approach
    
    Args:
        prices: List of stock prices by day
    
    Returns:
        Maximum profit achievable
    """
    if len(prices) < 2:
        return 0
    
    min_price = prices[0]
    max_profit = 0
    
    for price in prices[1:]:
        # Calculate profit if we sell at current price
        current_profit = price - min_price
        
        # Update maximum profit
        max_profit = max(max_profit, current_profit)
        
        # Update minimum price for future transactions
        min_price = min(min_price, price)
    
    return max_profit


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    prices1 = [7, 1, 5, 3, 6, 4]
    result1 = solution(prices1)
    print(f"Input: prices = {prices1}")
    print(f"Output: {result1}")
    print("Explanation: Buy at price 1, sell at price 6, profit = 5")
    print()
    
    # Test case 2
    prices2 = [7, 6, 4, 3, 1]
    result2 = solution(prices2)
    print(f"Input: prices = {prices2}")
    print(f"Output: {result2}")
    print("Explanation: Prices only decrease, no profit possible")
    print()
    
    # Test case 3
    prices3 = [1, 2, 3, 4, 5]
    result3 = solution(prices3)
    print(f"Input: prices = {prices3}")
    print(f"Output: {result3}")
    print("Explanation: Buy at price 1, sell at price 5, profit = 4")
    print()


if __name__ == "__main__":
    main()
