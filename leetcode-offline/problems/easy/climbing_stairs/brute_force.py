"""
Problem: Climbing Stairs
Approach: Recursion (Brute Force)
Time Complexity: O(2^n)
Space Complexity: O(n) - recursion stack

Explanation:
Use recursion to explore all possible ways to reach the top.
At each step, we can either take 1 step or 2 steps.

Key insights:
- Base cases: n=1 has 1 way, n=2 has 2 ways
- Recursive relation: f(n) = f(n-1) + f(n-2)
- This is essentially the Fibonacci sequence
- Exponential time due to repeated subproblems
"""


def solution(n):
    """
    Count ways to climb stairs using recursion
    
    Args:
        n: Number of steps to reach the top
    
    Returns:
        Number of distinct ways to climb to the top
    """
    # Base cases
    if n <= 2:
        return n
    
    # Recursive case: can reach n from (n-1) or (n-2)
    return solution(n - 1) + solution(n - 2)


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    n1 = 2
    result1 = solution(n1)
    print(f"Input: n = {n1}")
    print(f"Output: {result1}")
    print("Explanation: [1+1] or [2] = 2 ways")
    print()
    
    # Test case 2
    n2 = 3
    result2 = solution(n2)
    print(f"Input: n = {n2}")
    print(f"Output: {result2}")
    print("Explanation: [1+1+1], [1+2], [2+1] = 3 ways")
    print()
    
    # Test case 3
    n3 = 4
    result3 = solution(n3)
    print(f"Input: n = {n3}")
    print(f"Output: {result3}")
    print("Explanation: [1+1+1+1], [1+1+2], [1+2+1], [2+1+1], [2+2] = 5 ways")
    print()


if __name__ == "__main__":
    main()
