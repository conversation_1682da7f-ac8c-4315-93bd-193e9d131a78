"""
Problem: Climbing Stairs
Approach: Dynamic Programming (Optimized)
Time Complexity: O(n)
Space Complexity: O(1)

Explanation:
Use iterative approach to build up the solution from bottom to top.
Only keep track of the last two values since we only need f(n-1) and f(n-2).

Key insights:
- This is the <PERSON><PERSON><PERSON><PERSON> sequence: f(n) = f(n-1) + f(n-2)
- Use two variables to track previous two values
- Iterate from bottom up to avoid repeated calculations
- Constant space optimization
"""


def solution(n):
    """
    Count ways to climb stairs using dynamic programming
    
    Args:
        n: Number of steps to reach the top
    
    Returns:
        Number of distinct ways to climb to the top
    """
    if n <= 2:
        return n
    
    # Initialize for first two steps
    prev2 = 1  # f(1)
    prev1 = 2  # f(2)
    
    # Build up solution iteratively
    for i in range(3, n + 1):
        current = prev1 + prev2
        prev2 = prev1
        prev1 = current
    
    return prev1


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    n1 = 2
    result1 = solution(n1)
    print(f"Input: n = {n1}")
    print(f"Output: {result1}")
    print("Explanation: Base case, 2 ways")
    print()
    
    # Test case 2
    n2 = 3
    result2 = solution(n2)
    print(f"Input: n = {n2}")
    print(f"Output: {result2}")
    print("Explanation: f(3) = f(2) + f(1) = 2 + 1 = 3")
    print()
    
    # Test case 3
    n3 = 5
    result3 = solution(n3)
    print(f"Input: n = {n3}")
    print(f"Output: {result3}")
    print("Explanation: f(5) = f(4) + f(3) = 5 + 3 = 8")
    print()


if __name__ == "__main__":
    main()
