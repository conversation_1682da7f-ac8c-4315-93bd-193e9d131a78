"""
Problem: Contains Duplicate
Approach: Brute Force
Time Complexity: O(n²)
Space Complexity: O(1)

Explanation:
Check every pair of elements to see if any two are equal.
Use nested loops to compare each element with every other element.

Key insights:
- Compare each element with all subsequent elements
- Return True immediately when duplicate found
- No extra space needed
"""


def solution(nums):
    """
    Check if array contains duplicates using brute force
    
    Args:
        nums: List of integers
    
    Returns:
        Boolean indicating if duplicates exist
    """
    n = len(nums)
    
    # Compare each element with every other element
    for i in range(n):
        for j in range(i + 1, n):
            if nums[i] == nums[j]:
                return True
    
    return False


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [1, 2, 3, 1]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    nums2 = [1, 2, 3, 4]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    nums3 = [1, 1, 1, 3, 3, 4, 3, 2, 4, 2]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
