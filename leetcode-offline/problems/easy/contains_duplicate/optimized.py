"""
Problem: Contains Duplicate
Approach: Hash Set (Optimized)
Time Complexity: O(n)
Space Complexity: O(n)

Explanation:
Use a hash set to track elements we've seen.
For each element, check if it's already in the set.
If yes, we found a duplicate. If no, add it to the set.

Key insights:
- Hash set provides O(1) lookup and insertion
- Single pass through the array
- Early termination when duplicate found
- Trade space for time complexity improvement
"""


def solution(nums):
    """
    Check if array contains duplicates using hash set
    
    Args:
        nums: List of integers
    
    Returns:
        Boolean indicating if duplicates exist
    """
    seen = set()
    
    for num in nums:
        if num in seen:
            return True
        seen.add(num)
    
    return False


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [1, 2, 3, 1]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    nums2 = [1, 2, 3, 4]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    nums3 = [1, 1, 1, 3, 3, 4, 3, 2, 4, 2]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
