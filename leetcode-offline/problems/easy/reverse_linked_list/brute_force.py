"""
Problem: Reverse Linked List
Approach: Convert to Array and Back (Brute Force)
Time Complexity: O(n)
Space Complexity: O(n)

Explanation:
Convert the linked list to an array, reverse the array, then create a new
linked list from the reversed array.

Key insights:
- Extract all values into an array
- Reverse the array
- Create new linked list from reversed values
- Uses extra space but conceptually simple
"""


class ListNode:
    """Definition for singly-linked list node"""
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next


def list_to_linkedlist(arr):
    """Convert array to linked list"""
    if not arr:
        return None
    
    head = ListNode(arr[0])
    current = head
    for val in arr[1:]:
        current.next = ListNode(val)
        current = current.next
    return head


def linkedlist_to_list(head):
    """Convert linked list to array"""
    result = []
    current = head
    while current:
        result.append(current.val)
        current = current.next
    return result


def solution(head):
    """
    Reverse linked list by converting to array and back
    
    Args:
        head: Head of the linked list (ListNode or array for testing)
    
    Returns:
        Head of reversed linked list (array for testing)
    """
    # Handle array input for testing
    if isinstance(head, list):
        head = list_to_linkedlist(head)
    
    if not head:
        return []
    
    # Convert to array
    values = []
    current = head
    while current:
        values.append(current.val)
        current = current.next
    
    # Reverse array
    values.reverse()
    
    # Create new linked list from reversed array
    new_head = ListNode(values[0])
    current = new_head
    for val in values[1:]:
        current.next = ListNode(val)
        current = current.next
    
    # Return array for testing
    return linkedlist_to_list(new_head)


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    head1 = [1, 2, 3, 4, 5]
    result1 = solution(head1)
    print(f"Input: head = {head1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    head2 = [1, 2]
    result2 = solution(head2)
    print(f"Input: head = {head2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    head3 = []
    result3 = solution(head3)
    print(f"Input: head = {head3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
