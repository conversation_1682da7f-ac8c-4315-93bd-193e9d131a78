"""
Problem: Two Sum
Approach: Brute Force
Time Complexity: O(n²)
Space Complexity: O(1)

Explanation:
Use nested loops to check every pair of numbers in the array.
For each element, check if there exists another element that adds up to the target.

Key insights:
- Simple nested loop approach
- Check all possible pairs
- Return indices when sum equals target
"""


def solution(nums, target):
    """
    Find two numbers that add up to target using brute force
    
    Args:
        nums: List of integers
        target: Target sum
    
    Returns:
        List of two indices whose values add up to target
    """
    n = len(nums)
    
    # Check every pair of numbers
    for i in range(n):
        for j in range(i + 1, n):
            if nums[i] + nums[j] == target:
                return [i, j]
    
    # Should never reach here based on problem constraints
    return []


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [2, 7, 11, 15]
    target1 = 9
    result1 = solution(nums1, target1)
    print(f"Input: nums = {nums1}, target = {target1}")
    print(f"Output: {result1}")
    print(f"Verification: nums[{result1[0]}] + nums[{result1[1]}] = {nums1[result1[0]]} + {nums1[result1[1]]} = {nums1[result1[0]] + nums1[result1[1]]}")
    print()
    
    # Test case 2
    nums2 = [3, 2, 4]
    target2 = 6
    result2 = solution(nums2, target2)
    print(f"Input: nums = {nums2}, target = {target2}")
    print(f"Output: {result2}")
    print(f"Verification: nums[{result2[0]}] + nums[{result2[1]}] = {nums2[result2[0]]} + {nums2[result2[1]]} = {nums2[result2[0]] + nums2[result2[1]]}")
    print()
    
    # Test case 3
    nums3 = [3, 3]
    target3 = 6
    result3 = solution(nums3, target3)
    print(f"Input: nums = {nums3}, target = {target3}")
    print(f"Output: {result3}")
    print(f"Verification: nums[{result3[0]}] + nums[{result3[1]}] = {nums3[result3[0]]} + {nums3[result3[1]]} = {nums3[result3[0]] + nums3[result3[1]]}")


if __name__ == "__main__":
    main()
