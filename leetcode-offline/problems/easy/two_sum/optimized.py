"""
Problem: Two Sum
Approach: Hash Map (Optimized)
Time Complexity: O(n)
Space Complexity: O(n)

Explanation:
Use a hash map to store numbers we've seen and their indices.
For each number, check if its complement (target - current number) exists in the hash map.

Key insights:
- Hash map provides O(1) lookup time
- Store complement and index for quick access
- Single pass through the array
- Trade space for time complexity improvement
"""


def solution(nums, target):
    """
    Find two numbers that add up to target using hash map
    
    Args:
        nums: List of integers
        target: Target sum
    
    Returns:
        List of two indices whose values add up to target
    """
    # Hash map to store number -> index mapping
    num_to_index = {}
    
    for i, num in enumerate(nums):
        complement = target - num
        
        # Check if complement exists in hash map
        if complement in num_to_index:
            return [num_to_index[complement], i]
        
        # Store current number and its index
        num_to_index[num] = i
    
    # Should never reach here based on problem constraints
    return []


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [2, 7, 11, 15]
    target1 = 9
    result1 = solution(nums1, target1)
    print(f"Input: nums = {nums1}, target = {target1}")
    print(f"Output: {result1}")
    print(f"Verification: nums[{result1[0]}] + nums[{result1[1]}] = {nums1[result1[0]]} + {nums1[result1[1]]} = {nums1[result1[0]] + nums1[result1[1]]}")
    print()
    
    # Test case 2
    nums2 = [3, 2, 4]
    target2 = 6
    result2 = solution(nums2, target2)
    print(f"Input: nums = {nums2}, target = {target2}")
    print(f"Output: {result2}")
    print(f"Verification: nums[{result2[0]}] + nums[{result2[1]}] = {nums2[result2[0]]} + {nums2[result2[1]]} = {nums2[result2[0]] + nums2[result2[1]]}")
    print()
    
    # Test case 3
    nums3 = [3, 3]
    target3 = 6
    result3 = solution(nums3, target3)
    print(f"Input: nums = {nums3}, target = {target3}")
    print(f"Output: {result3}")
    print(f"Verification: nums[{result3[0]}] + nums[{result3[1]}] = {nums3[result3[0]]} + {nums3[result3[1]]} = {nums3[result3[0]] + nums3[result3[1]]}")


if __name__ == "__main__":
    main()
