"""
Problem: Valid Anagram
Approach: Sorting (Brute Force)
Time Complexity: O(n log n)
Space Complexity: O(1) or O(n) depending on sorting implementation

Explanation:
Sort both strings and compare if they are equal.
If two strings are anagrams, their sorted versions will be identical.

Key insights:
- Anagrams have the same characters with same frequencies
- Sorting arranges characters in the same order
- Simple comparison after sorting
- Built-in sorting is typically O(n log n)
"""


def solution(s, t):
    """
    Check if two strings are anagrams using sorting
    
    Args:
        s: First string
        t: Second string
    
    Returns:
        Boolean indicating if strings are anagrams
    """
    # Different lengths cannot be anagrams
    if len(s) != len(t):
        return False
    
    # Sort both strings and compare
    return sorted(s) == sorted(t)


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    s1, t1 = "anagram", "nagaram"
    result1 = solution(s1, t1)
    print(f"Input: s = \"{s1}\", t = \"{t1}\"")
    print(f"Output: {result1}")
    print(f"Sorted: \"{sorted(s1)}\" == \"{sorted(t1)}\"")
    print()
    
    # Test case 2
    s2, t2 = "rat", "car"
    result2 = solution(s2, t2)
    print(f"Input: s = \"{s2}\", t = \"{t2}\"")
    print(f"Output: {result2}")
    print(f"Sorted: \"{sorted(s2)}\" != \"{sorted(t2)}\"")
    print()
    
    # Test case 3
    s3, t3 = "listen", "silent"
    result3 = solution(s3, t3)
    print(f"Input: s = \"{s3}\", t = \"{t3}\"")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
