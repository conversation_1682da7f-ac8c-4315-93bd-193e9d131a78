"""
Problem: Valid Anagram
Approach: Character Frequency Count (Optimized)
Time Complexity: O(n)
Space Complexity: O(1) - at most 26 characters for lowercase English

Explanation:
Count the frequency of each character in both strings.
If the frequency maps are identical, the strings are anagrams.

Key insights:
- Use hash map to count character frequencies
- Compare frequency maps for equality
- Linear time complexity
- Constant space for fixed alphabet size
"""


def solution(s, t):
    """
    Check if two strings are anagrams using character frequency
    
    Args:
        s: First string
        t: Second string
    
    Returns:
        Boolean indicating if strings are anagrams
    """
    # Different lengths cannot be anagrams
    if len(s) != len(t):
        return False
    
    # Count character frequencies
    char_count = {}
    
    # Count characters in first string
    for char in s:
        char_count[char] = char_count.get(char, 0) + 1
    
    # Subtract character counts from second string
    for char in t:
        if char not in char_count:
            return False
        char_count[char] -= 1
        if char_count[char] == 0:
            del char_count[char]
    
    # If all characters are balanced, the map should be empty
    return len(char_count) == 0


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    s1, t1 = "anagram", "nagaram"
    result1 = solution(s1, t1)
    print(f"Input: s = \"{s1}\", t = \"{t1}\"")
    print(f"Output: {result1}")
    print("Explanation: Both have same character frequencies")
    print()
    
    # Test case 2
    s2, t2 = "rat", "car"
    result2 = solution(s2, t2)
    print(f"Input: s = \"{s2}\", t = \"{t2}\"")
    print(f"Output: {result2}")
    print("Explanation: Different character frequencies")
    print()
    
    # Test case 3
    s3, t3 = "listen", "silent"
    result3 = solution(s3, t3)
    print(f"Input: s = \"{s3}\", t = \"{t3}\"")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
