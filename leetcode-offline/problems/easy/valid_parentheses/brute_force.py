"""
Problem: Valid Parentheses
Approach: Recursive Replacement (Brute Force)
Time Complexity: O(n²)
Space Complexity: O(n)

Explanation:
Repeatedly remove valid pairs of parentheses until no more can be removed.
If the string becomes empty, it was valid; otherwise, it's invalid.

Key insights:
- Remove "()", "[]", "{}" pairs iteratively
- Continue until no more pairs can be removed
- Valid string will become empty
- Inefficient due to repeated string operations
"""


def solution(s):
    """
    Check if parentheses are valid using iterative replacement
    
    Args:
        s: String containing parentheses
    
    Returns:
        <PERSON><PERSON>an indicating if parentheses are valid
    """
    # Keep removing valid pairs until no more changes
    while True:
        original_length = len(s)
        
        # Remove all valid pairs
        s = s.replace("()", "")
        s = s.replace("[]", "")
        s = s.replace("{}", "")
        
        # If no changes were made, break
        if len(s) == original_length:
            break
    
    # Valid if string is empty
    return len(s) == 0


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    s1 = "()"
    result1 = solution(s1)
    print(f"Input: s = \"{s1}\"")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    s2 = "()[]{}"
    result2 = solution(s2)
    print(f"Input: s = \"{s2}\"")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    s3 = "(]"
    result3 = solution(s3)
    print(f"Input: s = \"{s3}\"")
    print(f"Output: {result3}")
    print()
    
    # Test case 4
    s4 = "([)]"
    result4 = solution(s4)
    print(f"Input: s = \"{s4}\"")
    print(f"Output: {result4}")
    print()


if __name__ == "__main__":
    main()
