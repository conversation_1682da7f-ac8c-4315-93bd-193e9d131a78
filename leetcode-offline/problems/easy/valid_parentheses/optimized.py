"""
Problem: Valid Parentheses
Approach: Stack (Optimized)
Time Complexity: O(n)
Space Complexity: O(n)

Explanation:
Use a stack to track opening brackets. When encountering a closing bracket,
check if it matches the most recent opening bracket on the stack.

Key insights:
- Stack follows LIFO (Last In, First Out) principle
- Push opening brackets onto stack
- Pop and match closing brackets
- Valid if stack is empty at the end
- Single pass through the string
"""


def solution(s):
    """
    Check if parentheses are valid using stack
    
    Args:
        s: String containing parentheses
    
    Returns:
        <PERSON><PERSON>an indicating if parentheses are valid
    """
    stack = []
    mapping = {')': '(', '}': '{', ']': '['}
    
    for char in s:
        if char in mapping:
            # Closing bracket
            if not stack or stack.pop() != mapping[char]:
                return False
        else:
            # Opening bracket
            stack.append(char)
    
    # Valid if all brackets are matched (stack is empty)
    return len(stack) == 0


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    s1 = "()"
    result1 = solution(s1)
    print(f"Input: s = \"{s1}\"")
    print(f"Output: {result1}")
    print("Explanation: Simple pair matches")
    print()
    
    # Test case 2
    s2 = "()[]{}"
    result2 = solution(s2)
    print(f"Input: s = \"{s2}\"")
    print(f"Output: {result2}")
    print("Explanation: All pairs match in order")
    print()
    
    # Test case 3
    s3 = "(]"
    result3 = solution(s3)
    print(f"Input: s = \"{s3}\"")
    print(f"Output: {result3}")
    print("Explanation: Mismatched bracket types")
    print()
    
    # Test case 4
    s4 = "([)]"
    result4 = solution(s4)
    print(f"Input: s = \"{s4}\"")
    print(f"Output: {result4}")
    print("Explanation: Wrong closing order")
    print()


if __name__ == "__main__":
    main()
