"""
Problem: 3Sum
Approach: Brute Force with Set
Time Complexity: O(n³)
Space Complexity: O(n)

Explanation:
Check all possible triplets using three nested loops.
Use a set to avoid duplicate triplets by sorting each triplet.

Key insights:
- Three nested loops to check all combinations
- Sort each triplet to handle duplicates
- Use set to store unique triplets
- Convert back to list of lists for output
"""


def solution(nums):
    """
    Find all unique triplets that sum to zero using brute force
    
    Args:
        nums: List of integers
    
    Returns:
        List of triplets that sum to zero
    """
    n = len(nums)
    triplets = set()
    
    # Check all possible triplets
    for i in range(n):
        for j in range(i + 1, n):
            for k in range(j + 1, n):
                if nums[i] + nums[j] + nums[k] == 0:
                    # Sort triplet to handle duplicates
                    triplet = tuple(sorted([nums[i], nums[j], nums[k]]))
                    triplets.add(triplet)
    
    # Convert set of tuples back to list of lists and sort for consistent output
    result = [list(triplet) for triplet in triplets]
    result.sort()
    return result


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [-1, 0, 1, 2, -1, -4]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    nums2 = [0, 1, 1]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    nums3 = [0, 0, 0]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
