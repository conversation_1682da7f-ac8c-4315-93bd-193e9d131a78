"""
Problem: 3Sum
Approach: Sort + Two Pointers (Optimized)
Time Complexity: O(n²)
Space Complexity: O(1) excluding output

Explanation:
Sort the array first, then for each element, use two pointers to find
pairs that sum to the negative of the current element.

Key insights:
- Sort array to enable two-pointer technique
- Fix first element, find two others using two pointers
- <PERSON>p duplicates to avoid duplicate triplets
- Two pointers converge from both ends
"""


def solution(nums):
    """
    Find all unique triplets that sum to zero using two pointers
    
    Args:
        nums: List of integers
    
    Returns:
        List of triplets that sum to zero
    """
    nums.sort()
    n = len(nums)
    result = []
    
    for i in range(n - 2):
        # Skip duplicate values for first element
        if i > 0 and nums[i] == nums[i - 1]:
            continue
        
        left, right = i + 1, n - 1
        
        while left < right:
            current_sum = nums[i] + nums[left] + nums[right]
            
            if current_sum == 0:
                result.append([nums[i], nums[left], nums[right]])
                
                # Skip duplicates for second element
                while left < right and nums[left] == nums[left + 1]:
                    left += 1
                # Skip duplicates for third element
                while left < right and nums[right] == nums[right - 1]:
                    right -= 1
                
                left += 1
                right -= 1
            elif current_sum < 0:
                left += 1
            else:
                right -= 1
    
    return result


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [-1, 0, 1, 2, -1, -4]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print("Explanation: After sorting [-4,-1,-1,0,1,2], use two pointers")
    print()
    
    # Test case 2
    nums2 = [0, 1, 1]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    nums3 = [0, 0, 0]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
