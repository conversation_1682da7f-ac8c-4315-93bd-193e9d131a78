"""
Problem: Container With Most Water
Approach: Brute Force
Time Complexity: O(n²)
Space Complexity: O(1)

Explanation:
Check all possible pairs of lines and calculate the area for each pair.
The area is determined by the shorter line and the distance between them.

Key insights:
- Area = min(height[i], height[j]) * (j - i)
- Check all pairs (i, j) where i < j
- Keep track of maximum area found
- Simple but inefficient approach
"""


def solution(height):
    """
    Find maximum water container area using brute force
    
    Args:
        height: List of line heights
    
    Returns:
        Maximum area that can be contained
    """
    max_area = 0
    n = len(height)
    
    # Check all possible pairs
    for i in range(n):
        for j in range(i + 1, n):
            # Area is limited by shorter line
            width = j - i
            min_height = min(height[i], height[j])
            area = width * min_height
            max_area = max(max_area, area)
    
    return max_area


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    height1 = [1, 8, 6, 2, 5, 4, 8, 3, 7]
    result1 = solution(height1)
    print(f"Input: height = {height1}")
    print(f"Output: {result1}")
    print("Explanation: Lines at index 1 and 8 give area = min(8,7) * (8-1) = 49")
    print()
    
    # Test case 2
    height2 = [1, 1]
    result2 = solution(height2)
    print(f"Input: height = {height2}")
    print(f"Output: {result2}")
    print("Explanation: Only one pair possible: min(1,1) * (1-0) = 1")
    print()
    
    # Test case 3
    height3 = [4, 3, 2, 1, 4]
    result3 = solution(height3)
    print(f"Input: height = {height3}")
    print(f"Output: {result3}")
    print("Explanation: Lines at index 0 and 4 give area = min(4,4) * (4-0) = 16")
    print()


if __name__ == "__main__":
    main()
