"""
Problem: Container With Most Water
Approach: Two Pointers (Optimized)
Time Complexity: O(n)
Space Complexity: O(1)

Explanation:
Start with two pointers at the ends of the array. Always move the pointer
with the smaller height inward, as moving the taller one cannot increase area.

Key insights:
- Start with maximum possible width
- Area is limited by the shorter line
- Moving the taller line inward only decreases width without increasing height
- Moving the shorter line might find a taller line
- Single pass through the array
"""


def solution(height):
    """
    Find maximum water container area using two pointers
    
    Args:
        height: List of line heights
    
    Returns:
        Maximum area that can be contained
    """
    left, right = 0, len(height) - 1
    max_area = 0
    
    while left < right:
        # Calculate current area
        width = right - left
        min_height = min(height[left], height[right])
        area = width * min_height
        max_area = max(max_area, area)
        
        # Move the pointer with smaller height
        if height[left] < height[right]:
            left += 1
        else:
            right -= 1
    
    return max_area


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    height1 = [1, 8, 6, 2, 5, 4, 8, 3, 7]
    result1 = solution(height1)
    print(f"Input: height = {height1}")
    print(f"Output: {result1}")
    print("Explanation: Two pointers find optimal pair efficiently")
    print()
    
    # Test case 2
    height2 = [1, 1]
    result2 = solution(height2)
    print(f"Input: height = {height2}")
    print(f"Output: {result2}")
    print("Explanation: Only one pair: area = 1 * 1 = 1")
    print()
    
    # Test case 3
    height3 = [4, 3, 2, 1, 4]
    result3 = solution(height3)
    print(f"Input: height = {height3}")
    print(f"Output: {result3}")
    print("Explanation: Start with ends (4,4), width=4, area=16")
    print()


if __name__ == "__main__":
    main()
