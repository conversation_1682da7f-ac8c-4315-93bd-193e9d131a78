"""
Problem: Group Anagrams
Approach: Nested Loop Comparison (Brute Force)
Time Complexity: O(n² * m log m) where n is number of strings, m is average string length
Space Complexity: O(n * m)

Explanation:
For each string, check all other strings to see if they are anagrams.
Use sorting to determine if two strings are anagrams.

Key insights:
- Compare each string with every other string
- Use sorting to check if strings are anagrams
- Group anagrams together as they are found
- Mark processed strings to avoid duplicates
"""


def solution(strs):
    """
    Group anagrams using brute force comparison
    
    Args:
        strs: List of strings
    
    Returns:
        List of groups, where each group contains anagrams
    """
    if not strs:
        return []
    
    result = []
    used = [False] * len(strs)
    
    for i in range(len(strs)):
        if used[i]:
            continue
            
        # Start a new group with current string
        group = [strs[i]]
        used[i] = True
        
        # Find all anagrams of current string
        for j in range(i + 1, len(strs)):
            if not used[j] and sorted(strs[i]) == sorted(strs[j]):
                group.append(strs[j])
                used[j] = True
        
        # Sort group for consistent output
        group.sort()
        result.append(group)
    
    # Sort result for consistent output
    result.sort()
    return result


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    strs1 = ["eat", "tea", "tan", "ate", "nat", "bat"]
    result1 = solution(strs1)
    print(f"Input: strs = {strs1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    strs2 = [""]
    result2 = solution(strs2)
    print(f"Input: strs = {strs2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    strs3 = ["a"]
    result3 = solution(strs3)
    print(f"Input: strs = {strs3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
