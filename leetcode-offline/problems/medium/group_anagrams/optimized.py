"""
Problem: Group Anagrams
Approach: Hash Map with Sorted Keys (Optimized)
Time Complexity: O(n * m log m) where n is number of strings, m is average string length
Space Complexity: O(n * m)

Explanation:
Use a hash map where the key is the sorted version of each string.
All anagrams will have the same sorted key, so they'll be grouped together.

Key insights:
- Anagrams have identical sorted character sequences
- Use sorted string as hash map key
- Group strings with same sorted key
- Single pass through the input
"""


def solution(strs):
    """
    Group anagrams using hash map with sorted keys
    
    Args:
        strs: List of strings
    
    Returns:
        List of groups, where each group contains anagrams
    """
    if not strs:
        return []
    
    anagram_groups = {}
    
    for s in strs:
        # Use sorted string as key
        key = ''.join(sorted(s))
        
        if key not in anagram_groups:
            anagram_groups[key] = []
        
        anagram_groups[key].append(s)
    
    # Sort each group and the result for consistent output
    result = []
    for group in anagram_groups.values():
        group.sort()
        result.append(group)
    
    result.sort()
    return result


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    strs1 = ["eat", "tea", "tan", "ate", "nat", "bat"]
    result1 = solution(strs1)
    print(f"Input: strs = {strs1}")
    print(f"Output: {result1}")
    print("Explanation: 'eat','tea','ate' -> 'aet', 'tan','nat' -> 'ant', 'bat' -> 'abt'")
    print()
    
    # Test case 2
    strs2 = [""]
    result2 = solution(strs2)
    print(f"Input: strs = {strs2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    strs3 = ["a"]
    result3 = solution(strs3)
    print(f"Input: strs = {strs3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
