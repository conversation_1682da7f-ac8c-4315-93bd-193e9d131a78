"""
Problem: Maximum Subarray
Approach: Brute Force
Time Complexity: O(n³)
Space Complexity: O(1)

Explanation:
Check all possible subarrays and find the one with maximum sum.
Use three nested loops: start index, end index, and sum calculation.

Key insights:
- Generate all possible subarrays
- Calculate sum for each subarray
- Keep track of maximum sum found
- Simple but inefficient approach
"""


def solution(nums):
    """
    Find maximum subarray sum using brute force
    
    Args:
        nums: List of integers
    
    Returns:
        Maximum sum of any contiguous subarray
    """
    n = len(nums)
    max_sum = float('-inf')
    
    # Try all possible subarrays
    for i in range(n):
        for j in range(i, n):
            # Calculate sum of subarray from i to j
            current_sum = 0
            for k in range(i, j + 1):
                current_sum += nums[k]
            
            max_sum = max(max_sum, current_sum)
    
    return max_sum


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [-2, 1, -3, 4, -1, 2, 1, -5, 4]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2
    nums2 = [1]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print()
    
    # Test case 3
    nums3 = [5, 4, -1, 7, 8]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
