"""
Problem: Maximum Subarray
Approach: <PERSON><PERSON><PERSON>'s Algorithm (Optimized)
Time Complexity: O(n)
Space Complexity: O(1)

Explanation:
Use <PERSON><PERSON><PERSON>'s algorithm - dynamic programming approach.
At each position, decide whether to extend the existing subarray or start a new one.
Keep track of the maximum sum seen so far.

Key insights:
- If current sum becomes negative, start fresh from current element
- Always keep track of the global maximum
- Single pass through the array
- Optimal solution for this problem
"""


def solution(nums):
    """
    Find maximum subarray sum using <PERSON><PERSON><PERSON>'s algorithm
    
    Args:
        nums: List of integers
    
    Returns:
        Maximum sum of any contiguous subarray
    """
    max_sum = nums[0]  # Global maximum
    current_sum = nums[0]  # Current subarray sum
    
    for i in range(1, len(nums)):
        # Either extend existing subarray or start new one
        current_sum = max(nums[i], current_sum + nums[i])
        
        # Update global maximum
        max_sum = max(max_sum, current_sum)
    
    return max_sum


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [-2, 1, -3, 4, -1, 2, 1, -5, 4]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print("Explanation: Subarray [4,-1,2,1] has sum 6")
    print()
    
    # Test case 2
    nums2 = [1]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print("Explanation: Single element subarray [1] has sum 1")
    print()
    
    # Test case 3
    nums3 = [5, 4, -1, 7, 8]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print("Explanation: Entire array [5,4,-1,7,8] has sum 23")
    print()


if __name__ == "__main__":
    main()
