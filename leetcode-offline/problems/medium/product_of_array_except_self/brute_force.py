"""
Problem: Product of Array Except Self
Approach: Brute Force
Time Complexity: O(n²)
Space Complexity: O(1) excluding output array

Explanation:
For each position i, calculate the product of all elements except nums[i]
by iterating through the entire array and skipping index i.

Key insights:
- For each element, multiply all other elements
- Skip the current index when calculating product
- Handle zero values carefully
- Simple but inefficient approach
"""


def solution(nums):
    """
    Calculate product of array except self using brute force
    
    Args:
        nums: List of integers
    
    Returns:
        List where result[i] = product of all elements except nums[i]
    """
    n = len(nums)
    result = []
    
    for i in range(n):
        product = 1
        for j in range(n):
            if i != j:
                product *= nums[j]
        result.append(product)
    
    return result


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [1, 2, 3, 4]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print("Explanation: [2*3*4, 1*3*4, 1*2*4, 1*2*3] = [24, 12, 8, 6]")
    print()
    
    # Test case 2
    nums2 = [-1, 1, 0, -3, 3]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print("Explanation: Zero at index 2 makes most products zero")
    print()
    
    # Test case 3
    nums3 = [2, 3, 4, 5]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
