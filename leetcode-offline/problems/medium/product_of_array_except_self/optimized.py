"""
Problem: Product of Array Except Self
Approach: Left and Right Products (Optimized)
Time Complexity: O(n)
Space Complexity: O(1) excluding output array

Explanation:
Use two passes to calculate left and right products:
1. First pass: Calculate left products (product of all elements to the left)
2. Second pass: Calculate right products and multiply with left products

Key insights:
- Left products: result[i] = product of nums[0] to nums[i-1]
- Right products: multiply result[i] with product of nums[i+1] to nums[n-1]
- Use the result array to store left products, then multiply with right products
- Single array solution with O(1) extra space
"""


def solution(nums):
    """
    Calculate product of array except self using left/right products
    
    Args:
        nums: List of integers
    
    Returns:
        List where result[i] = product of all elements except nums[i]
    """
    n = len(nums)
    result = [1] * n
    
    # First pass: calculate left products
    # result[i] = product of all elements to the left of i
    for i in range(1, n):
        result[i] = result[i - 1] * nums[i - 1]
    
    # Second pass: calculate right products and multiply with left products
    # right_product keeps track of product of all elements to the right
    right_product = 1
    for i in range(n - 1, -1, -1):
        result[i] *= right_product
        right_product *= nums[i]
    
    return result


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [1, 2, 3, 4]
    result1 = solution(nums1)
    print(f"Input: nums = {nums1}")
    print(f"Output: {result1}")
    print("Explanation: Left products [1,1,2,6] * Right products [24,12,4,1] = [24,12,8,6]")
    print()
    
    # Test case 2
    nums2 = [-1, 1, 0, -3, 3]
    result2 = solution(nums2)
    print(f"Input: nums = {nums2}")
    print(f"Output: {result2}")
    print("Explanation: Zero propagates through products")
    print()
    
    # Test case 3
    nums3 = [2, 3, 4, 5]
    result3 = solution(nums3)
    print(f"Input: nums = {nums3}")
    print(f"Output: {result3}")
    print()


if __name__ == "__main__":
    main()
