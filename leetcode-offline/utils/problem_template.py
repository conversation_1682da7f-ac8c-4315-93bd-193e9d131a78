"""
Template generator for creating new problems
"""
import os


class ProblemTemplate:
    
    @staticmethod
    def get_problem_template() -> str:
        """Get template for problem.txt"""
        return """Problem: [Problem Title]
Difficulty: [Easy/Medium/Hard]
Source: LeetCode

Description:
[Detailed problem description]

Example 1:
Input: [example input]
Output: [example output]
Explanation: [explanation]

Example 2:
Input: [example input]
Output: [example output]
Explanation: [explanation]

Constraints:
- [constraint 1]
- [constraint 2]
- [constraint 3]

Follow-up: [optional follow-up question]
"""
    
    @staticmethod
    def get_input_template() -> str:
        """Get template for input.txt"""
        return """# Test case inputs (one per line)
# Format: [input1, input2, ...] for multiple parameters
# Format: input for single parameter
[2, 7, 11, 15], 9
[3, 2, 4], 6
[3, 3], 6
"""
    
    @staticmethod
    def get_output_template() -> str:
        """Get template for output.txt"""
        return """# Expected outputs (one per line, corresponding to inputs)
[0, 1]
[1, 2]
[0, 1]
"""
    
    @staticmethod
    def get_solution_template(approach: str, problem_name: str) -> str:
        """Get template for solution files"""
        return f'''"""
Problem: {problem_name.replace('_', ' ').title()}
Approach: {approach.title()}
Time Complexity: O(?)
Space Complexity: O(?)

Explanation:
[Explain your approach here]

Key insights:
- [Key insight 1]
- [Key insight 2]
"""


def solution(nums, target):
    """
    Main solution function
    
    Args:
        nums: [parameter description]
        target: [parameter description]
    
    Returns:
        [return description]
    """
    # TODO: Implement your solution here
    pass


def main():
    """Test the solution with sample inputs"""
    # Test case 1
    nums1 = [2, 7, 11, 15]
    target1 = 9
    result1 = solution(nums1, target1)
    print(f"Input: nums = {{nums1}}, target = {{target1}}")
    print(f"Output: {{result1}}")
    print()
    
    # Test case 2
    nums2 = [3, 2, 4]
    target2 = 6
    result2 = solution(nums2, target2)
    print(f"Input: nums = {{nums2}}, target = {{target2}}")
    print(f"Output: {{result2}}")
    print()


if __name__ == "__main__":
    main()
'''
    
    @staticmethod
    def create_problem_structure(problem_name: str, difficulty: str, base_dir: str = "problems"):
        """Create complete problem directory structure with templates"""
        # Create problem directory
        problem_dir = os.path.join(base_dir, difficulty, problem_name)
        os.makedirs(problem_dir, exist_ok=True)
        
        # Create problem.txt
        problem_file = os.path.join(problem_dir, "problem.txt")
        with open(problem_file, 'w') as f:
            f.write(ProblemTemplate.get_problem_template())
        
        # Create input.txt
        input_file = os.path.join(problem_dir, "input.txt")
        with open(input_file, 'w') as f:
            f.write(ProblemTemplate.get_input_template())
        
        # Create output.txt
        output_file = os.path.join(problem_dir, "output.txt")
        with open(output_file, 'w') as f:
            f.write(ProblemTemplate.get_output_template())
        
        # Create brute_force.py
        brute_force_file = os.path.join(problem_dir, "brute_force.py")
        with open(brute_force_file, 'w') as f:
            f.write(ProblemTemplate.get_solution_template("brute force", problem_name))
        
        # Create optimized.py
        optimized_file = os.path.join(problem_dir, "optimized.py")
        with open(optimized_file, 'w') as f:
            f.write(ProblemTemplate.get_solution_template("optimized", problem_name))
        
        return problem_dir
    
    @staticmethod
    def list_problems(base_dir: str = "problems") -> dict:
        """List all available problems organized by difficulty"""
        problems = {"easy": [], "medium": [], "hard": []}
        
        if not os.path.exists(base_dir):
            return problems
        
        for difficulty in ["easy", "medium", "hard"]:
            diff_dir = os.path.join(base_dir, difficulty)
            if os.path.exists(diff_dir):
                for item in os.listdir(diff_dir):
                    item_path = os.path.join(diff_dir, item)
                    if os.path.isdir(item_path):
                        problems[difficulty].append(item)
        
        return problems
    
    @staticmethod
    def get_problem_info(problem_name: str, difficulty: str = None, base_dir: str = "problems") -> dict:
        """Get information about a specific problem"""
        # Find problem directory
        problem_path = None
        if difficulty:
            potential_path = os.path.join(base_dir, difficulty, problem_name)
            if os.path.exists(potential_path):
                problem_path = potential_path
        else:
            # Search in all difficulties
            for diff in ['easy', 'medium', 'hard']:
                potential_path = os.path.join(base_dir, diff, problem_name)
                if os.path.exists(potential_path):
                    problem_path = potential_path
                    difficulty = diff
                    break
        
        if not problem_path:
            return {"error": f"Problem '{problem_name}' not found"}
        
        info = {
            "name": problem_name,
            "difficulty": difficulty,
            "path": problem_path,
            "files": {}
        }
        
        # Check which files exist
        files_to_check = ["problem.txt", "input.txt", "output.txt", "brute_force.py", "optimized.py"]
        for file_name in files_to_check:
            file_path = os.path.join(problem_path, file_name)
            info["files"][file_name] = os.path.exists(file_path)
        
        return info
