"""
Test runner for executing and comparing solutions
"""
import os
import sys
import time
import importlib.util
from typing import List, <PERSON><PERSON>, Any, Dict


class TestRunner:
    def __init__(self, problems_dir: str = "problems"):
        self.problems_dir = problems_dir
    
    def load_test_cases(self, problem_path: str) -> Tuple[List[Any], List[Any]]:
        """Load input and expected output test cases from files"""
        input_file = os.path.join(problem_path, "input.txt")
        output_file = os.path.join(problem_path, "output.txt")
        
        inputs = []
        outputs = []
        
        # Load inputs
        if os.path.exists(input_file):
            with open(input_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # Parse input (assuming JSON-like format)
                        try:
                            inputs.append(eval(line))
                        except:
                            inputs.append(line)
        
        # Load expected outputs
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            # Handle boolean values specifically
                            if line.lower() == 'true':
                                outputs.append(True)
                            elif line.lower() == 'false':
                                outputs.append(False)
                            else:
                                outputs.append(eval(line))
                        except:
                            outputs.append(line)
        
        return inputs, outputs
    
    def load_solution(self, solution_path: str) -> Any:
        """Dynamically load a solution module"""
        if not os.path.exists(solution_path):
            return None

        spec = importlib.util.spec_from_file_location("solution", solution_path)
        if spec is None or spec.loader is None:
            return None

        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)

        return module
    
    def run_solution(self, solution_module: Any, test_input: Any) -> Tuple[Any, float]:
        """Run a solution and measure execution time"""
        if not hasattr(solution_module, 'solution'):
            raise AttributeError("Solution module must have a 'solution' function")
        
        start_time = time.time()
        try:
            if isinstance(test_input, (list, tuple)) and len(test_input) > 1:
                result = solution_module.solution(*test_input)
            else:
                result = solution_module.solution(test_input)
        except Exception as e:
            return f"Error: {str(e)}", 0
        
        execution_time = time.time() - start_time
        return result, execution_time
    
    def run_problem(self, problem_name: str, difficulty: str = None) -> Dict:
        """Run both solutions for a problem and compare results"""
        # Find problem directory
        problem_path = None
        if difficulty:
            potential_path = os.path.join(self.problems_dir, difficulty, problem_name)
            if os.path.exists(potential_path):
                problem_path = potential_path
        else:
            # Search in all difficulties
            for diff in ['easy', 'medium', 'hard']:
                potential_path = os.path.join(self.problems_dir, diff, problem_name)
                if os.path.exists(potential_path):
                    problem_path = potential_path
                    difficulty = diff
                    break
        
        if not problem_path:
            return {"error": f"Problem '{problem_name}' not found"}
        
        # Load test cases
        inputs, expected_outputs = self.load_test_cases(problem_path)
        
        if not inputs or not expected_outputs:
            return {"error": "No test cases found"}
        
        # Load solutions
        brute_force_path = os.path.join(problem_path, "brute_force.py")
        optimized_path = os.path.join(problem_path, "optimized.py")
        
        brute_force_module = self.load_solution(brute_force_path)
        optimized_module = self.load_solution(optimized_path)
        
        results = {
            "problem": problem_name,
            "difficulty": difficulty,
            "test_cases": len(inputs),
            "brute_force": {"available": brute_force_module is not None},
            "optimized": {"available": optimized_module is not None}
        }
        
        # Run brute force solution
        if brute_force_module:
            bf_results = []
            bf_times = []
            bf_passed = 0
            
            for test_input, expected in zip(inputs, expected_outputs):
                result, exec_time = self.run_solution(brute_force_module, test_input)
                passed = result == expected
                bf_results.append({"input": test_input, "output": result, "expected": expected, "passed": passed, "time": exec_time})
                bf_times.append(exec_time)
                if passed:
                    bf_passed += 1
            
            results["brute_force"].update({
                "passed": bf_passed,
                "total": len(inputs),
                "avg_time": sum(bf_times) / len(bf_times) if bf_times else 0,
                "results": bf_results
            })
        
        # Run optimized solution
        if optimized_module:
            opt_results = []
            opt_times = []
            opt_passed = 0
            
            for test_input, expected in zip(inputs, expected_outputs):
                result, exec_time = self.run_solution(optimized_module, test_input)
                passed = result == expected
                opt_results.append({"input": test_input, "output": result, "expected": expected, "passed": passed, "time": exec_time})
                opt_times.append(exec_time)
                if passed:
                    opt_passed += 1
            
            results["optimized"].update({
                "passed": opt_passed,
                "total": len(inputs),
                "avg_time": sum(opt_times) / len(opt_times) if opt_times else 0,
                "results": opt_results
            })
        
        return results
    
    def print_results(self, results: Dict):
        """Print formatted test results"""
        if "error" in results:
            print(f"❌ {results['error']}")
            return
        
        problem = results["problem"]
        difficulty = results["difficulty"]
        
        print(f"\n🎯 Problem: {problem.replace('_', ' ').title()} ({difficulty.title()})")
        print("=" * 60)
        
        # Brute Force Results
        if results["brute_force"]["available"]:
            bf = results["brute_force"]
            status = "✅" if bf["passed"] == bf["total"] else "❌"
            print(f"{status} Brute Force: {bf['passed']}/{bf['total']} passed (avg: {bf['avg_time']:.6f}s)")
        else:
            print("❌ Brute Force: Not available")
        
        # Optimized Results
        if results["optimized"]["available"]:
            opt = results["optimized"]
            status = "✅" if opt["passed"] == opt["total"] else "❌"
            print(f"{status} Optimized: {opt['passed']}/{opt['total']} passed (avg: {opt['avg_time']:.6f}s)")
        else:
            print("❌ Optimized: Not available")
        
        # Performance comparison
        if (results["brute_force"]["available"] and results["optimized"]["available"] and
            results["brute_force"]["passed"] > 0 and results["optimized"]["passed"] > 0):
            bf_time = results["brute_force"]["avg_time"]
            opt_time = results["optimized"]["avg_time"]
            if bf_time > 0 and opt_time > 0:
                speedup = bf_time / opt_time
                print(f"⚡ Speedup: {speedup:.2f}x faster")
        
        print()
